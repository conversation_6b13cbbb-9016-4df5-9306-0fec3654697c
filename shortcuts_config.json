{"shortcuts": [{"name": "Send Photos to Server", "description": "Select and send photos to your local Flask server", "actions": [{"type": "SelectPhotos", "parameters": {"selectMultiple": true, "selectLimit": 10}}, {"type": "GetMyIP", "parameters": {}}, {"type": "Text", "parameters": {"text": "http://**************:5000/upload/image"}}, {"type": "RepeatWithEach", "parameters": {"items": "Selected Photos"}, "actions": [{"type": "EncodeMedia", "parameters": {"encoding": "Base64"}}, {"type": "GetContentsOfURL", "parameters": {"url": "Text from previous action", "method": "POST", "headers": {"Content-Type": "application/json"}, "requestBody": {"image": "Base64 Encoded Media"}}}]}, {"type": "ShowNotification", "parameters": {"title": "Photos Uploaded", "body": "Successfully sent photos to server"}}]}, {"name": "Send Videos to Server", "description": "Select and send videos to your local Flask server", "actions": [{"type": "SelectPhotos", "parameters": {"selectMultiple": true, "selectLimit": 5, "mediaType": "Videos"}}, {"type": "Text", "parameters": {"text": "http://**************:5000/upload/video"}}, {"type": "RepeatWithEach", "parameters": {"items": "Selected Videos"}, "actions": [{"type": "EncodeMedia", "parameters": {"encoding": "Base64"}}, {"type": "GetContentsOfURL", "parameters": {"url": "Text from previous action", "method": "POST", "headers": {"Content-Type": "application/json"}, "requestBody": {"video": "Base64 Encoded Media", "extension": "mp4"}}}]}, {"type": "ShowNotification", "parameters": {"title": "Videos Uploaded", "body": "Successfully sent videos to server"}}]}, {"name": "Send Contacts to Server", "description": "Export and send contacts to your local Flask server", "actions": [{"type": "GetContacts", "parameters": {"selectMultiple": true}}, {"type": "GetDetailsOfContacts", "parameters": {"details": ["Name", "Phone Numbers", "Email Addresses", "Organization"]}}, {"type": "Text", "parameters": {"text": "http://**************:5000/upload/contacts"}}, {"type": "GetContentsOfURL", "parameters": {"url": "Text from previous action", "method": "POST", "headers": {"Content-Type": "application/json"}, "requestBody": {"contacts": "Contact Details"}}}, {"type": "ShowNotification", "parameters": {"title": "Contacts Uploaded", "body": "Successfully sent contacts to server"}}]}, {"name": "iPhone Data Sync", "description": "One-tap sync of photos, videos, and contacts", "actions": [{"type": "AskForInput", "parameters": {"inputType": "<PERSON><PERSON> from Menu", "prompt": "What would you like to sync?", "options": ["Photos Only", "Videos Only", "Contacts Only", "Photos & Videos", "Everything"]}}, {"type": "ChooseFromMenu", "parameters": {"items": "<PERSON><PERSON>"}, "cases": [{"case": "Photos Only", "actions": [{"type": "RunShortcut", "parameters": {"shortcut": "Send Photos to Server"}}]}, {"case": "Videos Only", "actions": [{"type": "RunShortcut", "parameters": {"shortcut": "Send Videos to Server"}}]}, {"case": "Contacts Only", "actions": [{"type": "RunShortcut", "parameters": {"shortcut": "Send Contacts to Server"}}]}, {"case": "Photos & Videos", "actions": [{"type": "RunShortcut", "parameters": {"shortcut": "Send Photos to Server"}}, {"type": "RunShortcut", "parameters": {"shortcut": "Send Videos to Server"}}]}, {"case": "Everything", "actions": [{"type": "RunShortcut", "parameters": {"shortcut": "Send Photos to Server"}}, {"type": "RunShortcut", "parameters": {"shortcut": "Send Videos to Server"}}, {"type": "RunShortcut", "parameters": {"shortcut": "Send Contacts to Server"}}]}]}]}], "setup_instructions": {"server_ip": "Server IP is set to ************** - ready to use", "permissions": "Grant Shortcuts app access to Photos and Contacts when prompted", "network": "Ensure iPhone and server are on the same WiFi network"}}