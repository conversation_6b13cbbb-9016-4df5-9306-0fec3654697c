#!/usr/bin/env python3
"""
Quick Setup Script for iPhone Data Server
Shows server status and provides copy-paste URLs for shortcuts
"""

import requests
import json

SERVER_IP = "**************"
SERVER_PORT = "5000"
BASE_URL = f"http://{SERVER_IP}:{SERVER_PORT}"

def check_server_status():
    """Check if the server is running and accessible"""
    try:
        response = requests.get(f"{BASE_URL}/status", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        else:
            return False, f"Server returned status code: {response.status_code}"
    except requests.exceptions.ConnectionError:
        return False, "Cannot connect to server. Make sure the Flask server is running."
    except requests.exceptions.Timeout:
        return False, "Connection timeout. Check your network connection."
    except Exception as e:
        return False, f"Error: {str(e)}"

def print_setup_info():
    """Print setup information and URLs"""
    print("=" * 60)
    print("📱 IPHONE DATA SERVER - QUICK SETUP")
    print("=" * 60)
    
    # Check server status
    print("\n🔍 Checking server status...")
    is_running, status_info = check_server_status()
    
    if is_running:
        print("✅ Server is running successfully!")
        print(f"📊 Stats: {status_info.get('stats', {})}")
    else:
        print(f"❌ Server issue: {status_info}")
        print("\n💡 To start the server, run: python app.py")
        return
    
    print(f"\n🌐 Server IP: {SERVER_IP}")
    print(f"🔗 Web Dashboard: {BASE_URL}")
    
    print("\n" + "=" * 60)
    print("📋 COPY-PASTE URLS FOR IPHONE SHORTCUTS")
    print("=" * 60)
    
    print("\n📸 For Photo Upload Shortcut:")
    print(f"   {BASE_URL}/upload/image")
    
    print("\n🎥 For Video Upload Shortcut:")
    print(f"   {BASE_URL}/upload/video")
    
    print("\n📞 For Contacts Upload Shortcut:")
    print(f"   {BASE_URL}/upload/contacts")
    
    print("\n📊 For Status Check:")
    print(f"   {BASE_URL}/status")
    
    print("\n" + "=" * 60)
    print("🚀 QUICK SHORTCUTS SETUP")
    print("=" * 60)
    
    print("\n1️⃣ SEND PHOTOS TO SERVER")
    print("   • Open Shortcuts app → Create new shortcut")
    print("   • Add: Select Photos (multiple, limit 10)")
    print(f"   • Add: Text → {BASE_URL}/upload/image")
    print("   • Add: Repeat with Each → Encode Media (Base64)")
    print("   • Add: Get Contents of URL (POST, JSON body)")
    
    print("\n2️⃣ SEND VIDEOS TO SERVER")
    print("   • Similar to photos but:")
    print(f"   • URL: {BASE_URL}/upload/video")
    print("   • Select Videos only")
    
    print("\n3️⃣ SEND CONTACTS TO SERVER")
    print("   • Add: Get Contacts (multiple)")
    print("   • Add: Get Details of Contacts")
    print(f"   • URL: {BASE_URL}/upload/contacts")
    
    print("\n" + "=" * 60)
    print("📱 TESTING YOUR SETUP")
    print("=" * 60)
    
    print(f"\n🌐 Open in browser: {BASE_URL}")
    print("📊 Check upload statistics")
    print("🧪 Run test script: python test_server.py")
    
    print("\n" + "=" * 60)
    print("🔧 TROUBLESHOOTING")
    print("=" * 60)
    
    print("\n❓ If iPhone can't connect:")
    print("   • Both devices on same WiFi?")
    print("   • Firewall blocking port 5000?")
    print(f"   • Try: {BASE_URL} in iPhone browser")
    
    print("\n❓ If shortcuts fail:")
    print("   • Grant Photos/Contacts permissions")
    print("   • Check JSON format in request body")
    print("   • Test individual actions first")
    
    print("\n🎉 Happy syncing! Your iPhone → Computer data transfer is ready!")
    print("=" * 60)

if __name__ == "__main__":
    print_setup_info()
