# iPhone Data Sync Shortcuts
# Import this file into iOS Shortcuts app
# Server IP: **************:5000

# Shortcut 1: Send Photos to Server
Name: Send Photos to Server
Actions:
1. Select Photos (Multiple: Yes, Limit: 10)
2. Text: http://**************:5000/upload/image
3. Repeat with Each (Photos)
   - Encode Media (Base64)
   - Get Contents of URL
     - URL: Text from step 2
     - Method: POST
     - Headers: Content-Type: application/json
     - Body: {"image": "[Base64 Encoded Media]"}
4. Show Notification: "Photos uploaded successfully"

# Shortcut 2: Send Videos to Server  
Name: Send Videos to Server
Actions:
1. Select Photos (Videos Only, Multiple: Yes, Limit: 5)
2. Text: http://**************:5000/upload/video
3. Repeat with Each (Videos)
   - Encode Media (Base64)
   - Get Contents of URL
     - URL: Text from step 2
     - Method: POST
     - Headers: Content-Type: application/json
     - Body: {"video": "[Base64 Encoded Media]", "extension": "mp4"}
4. Show Notification: "Videos uploaded successfully"

# Shortcut 3: Send Contacts to Server
Name: Send Contacts to Server
Actions:
1. Get Contacts (Multiple: Yes)
2. Get Details of Contacts (Name, Phone Numbers, Email Addresses, Organization)
3. Text: http://**************:5000/upload/contacts
4. Get Contents of URL
   - URL: Text from step 3
   - Method: POST
   - Headers: Content-Type: application/json
   - Body: {"contacts": "[Contact Details]"}
5. Show Notification: "Contacts uploaded successfully"

# Shortcut 4: iPhone Data Sync (Master)
Name: iPhone Data Sync
Actions:
1. Ask for Input
   - Type: Choose from Menu
   - Prompt: "What would you like to sync?"
   - Options: Photos Only, Videos Only, Contacts Only, Photos & Videos, Everything
2. Choose from Menu (Chosen Item)
   - Photos Only: Run Shortcut "Send Photos to Server"
   - Videos Only: Run Shortcut "Send Videos to Server"  
   - Contacts Only: Run Shortcut "Send Contacts to Server"
   - Photos & Videos: Run "Send Photos to Server" + "Send Videos to Server"
   - Everything: Run all three shortcuts

# Setup Instructions:
# 1. Create each shortcut manually in iOS Shortcuts app
# 2. Copy the exact URLs and settings shown above
# 3. Grant permissions for Photos and Contacts when prompted
# 4. Test with a few items first
# 5. Add to Home Screen for quick access

# Server Status: http://**************:5000/status
# Web Dashboard: http://**************:5000
